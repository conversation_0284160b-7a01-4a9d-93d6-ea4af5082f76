{"name": "shadowflow", "private": true, "homepage": ".", "scripts": {"lerna": "node node_modules/lerna/cli.js", "commit": "git-cz", "std": "yarn workspace @shadowflow/std run", "asset": "yarn workspace @shadowflow/asset run"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint", "prettier"], "*.{css,less}": ["stylelint"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "dependencies": {"@ant-design/icons": "^4.2.2", "@ant-design/pro-table": "^2.42.1", "@babel/plugin-proposal-decorators": "^7.10.5", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "antd": "^4.15.3", "axios": "^0.19.2", "babel-plugin-import": "^1.13.0", "customize-cra": "^1.0.0", "d3": "^7.6.1", "d3-array": "^3.2.0", "d3-sankey": "^0.12.3", "dagre-d3": "^0.6.4", "history": "4.9.0", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.0", "i18next": "^19.7.0", "ip": "^1.1.5", "ip-address": "^6.3.0", "jszip": "^3.7.1", "less": "^3.12.2", "less-loader": "^6.2.0", "lodash": "^4.17.19", "md5": "^2.3.0", "mobx": "^5.15.5", "mobx-react": "^6.2.5", "moment": "^2.27.0", "qs": "^6.9.4", "rc-util": "^5.13.1", "react": "^16.14", "react-app-rewired": "^2.1.6", "react-dnd": "^14.0.3", "react-dnd-html5-backend": "^14.0.1", "react-dom": "^16.13.1", "react-flag-icon-css": "^1.0.25", "react-highlight-words": "^0.17.0", "react-i18next": "^11.7.2", "react-router-dom": "^5.2.0", "react-scripts": "^4.0.3", "source-map-explorer": "^2.5.2", "xlsx": "^0.16.8"}, "devDependencies": {"@commitlint/cli": "^9.1.1", "@commitlint/config-conventional": "^9.1.1", "@typescript-eslint/eslint-plugin": "^3.9.0", "@typescript-eslint/parser": "^3.9.0", "babel-eslint": "^10.1.0", "commitizen": "^4.1.2", "cz-conventional-changelog": "^3.2.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^6.11.0", "eslint-config-react-app": "^5.2.1", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.0.8", "husky": "^4.2.5", "inquirer": "^7.3.3", "lerna": "^4.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ssh2": "^0.8.9", "standard-version": "^9.0.0", "stylelint": "^13.6.1", "stylelint-config-standard": "^20.0.0", "typescript": "^3.9.7"}, "workspaces": ["packages/*"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": "./.eslintrc"}}
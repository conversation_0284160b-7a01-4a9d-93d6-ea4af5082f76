// ============== checkbox ==============
.ant-checkbox-wrapper {
    color: var(--text-desc);
}
.ant-checkbox-inner {
    background-color: var(--bg-default);
    border-color: var(--border-default);
}
.ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--color-blue);
    border-color: var(--border-active);
}
.ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: var(--bg-default);
}
.ant-checkbox-indeterminate .ant-checkbox-inner {
    background-color: var(--bg-default);
    border-color: var(--border-default);
}
.ant-checkbox-indeterminate .ant-checkbox-inner::after {
    background-color: var(--color-blue);
}
.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: var(--border-hover);
}

/// =============================== 表单以及输入组件 ===============================

// ============== form ==============
.ant-form {
    color: var(--text-desc);
}
.ant-form-item {
    color: var(--text-desc);
}
.ant-form-item-label > label {
    color: var(--text-desc);
}
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-affix-wrapper,
.ant-form-item-has-error .ant-input:hover,
.ant-form-item-has-error .ant-input-affix-wrapper:hover {
    background-color: var(--bg-default);
    .ant-input {
        background-color: inherit;
    }
}

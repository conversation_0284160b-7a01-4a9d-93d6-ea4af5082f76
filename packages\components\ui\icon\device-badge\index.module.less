.device-badge {
    :global {
        position: absolute;
        .ti-red {
            color: var(--color-red);
            border-color: var(--color-red);
        }
        .ti-green {
            color: var(--color-green);
            border-color: var(--color-green);
        }
        .ti-blue {
            color: var(--color-blue);
            border-color: var(--color-blue);
        }
        .ti-black {
            color: rgb(105, 105, 105);
            border-color: rgb(105, 105, 105);
        }
        .unknow {
            color: var(--color-lightgray);
            border-color: var(--color-lightgray);
        }
        .device-badge-content {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            border-style: solid;
            border-width: 2px;
            border-radius: 50%;
            transform: rotateZ(30deg);
            font-size: inherit;
            .device-badge-ring {
                width: calc(100% - 20px);
                height: calc(100% - 20px);
                border-style: dashed;
                border-width: 1px;
                border-radius: 50%;
                border-color: inherit;
            }
            .device-badge-text {
                position: absolute;
                width: inherit;
                word-wrap: break-word;
                white-space: pre-wrap;
                word-break: break-all;
                letter-spacing: 1px;
                font-size: inherit;
                text-align: center;
            }
        }
    }
}

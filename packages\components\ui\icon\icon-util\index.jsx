import React from 'react'
import { Tooltip } from 'antd'
import Icon, {
    ReloadOutlined,
    // StarOutlined,
    // EyeInvisibleOutlined,
    SettingOutlined,
    EditOutlined,
    BellOutlined,
    ColumnHeightOutlined,
    // UploadOutlined,
    PartitionOutlined,
} from '@ant-design/icons'

const handleClick = (e, props) => {
    // 是否冒泡
    if (!props.isPropa) e.stopPropagation()
    if (props.onClick) props.onClick(e)
}

export function FreshIcon({ className, style, ...otherProps }) {
    return (
        <Tooltip title='刷新'>
            <ReloadOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}
// export function AddMo({ className, style, ...otherProps }) {
//     return (
//         <MoModalTrigger {...otherProps}>
//             <Tooltip title='增加Mo'>
//                 <StarOutlined className={className} style={style} />
//             </Tooltip>
//         </MoModalTrigger>
//     )
// }
// export function AddEventIgnore(props) {
//     return (
//         <EventIgnoreModalTrigger {...props}>
//             <Tooltip title='增加事件忽略名单'>
//                 <EyeInvisibleOutlined />
//             </Tooltip>
//         </EventIgnoreModalTrigger>
//     )
// }
export function EditEvent({ className, style, ...otherProps }) {
    return (
        <Tooltip title='设置事件配置'>
            <SettingOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}
export function Edit({ className, style, ...otherProps }) {
    return (
        <Tooltip title='编辑'>
            <EditOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}
export function AddEvent({ className, style, ...otherProps }) {
    return (
        <Tooltip title='添加事件'>
            <BellOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}
export function Relation({ className, style, ...otherProps }) {
    return (
        <Tooltip title='查看关联'>
            <PartitionOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}

export function Expand({ className, style, ...otherProps }) {
    return (
        <Tooltip title='扩展信息'>
            <ColumnHeightOutlined
                className={className}
                style={style}
                onClick={e => handleClick(e, otherProps)}
            />
        </Tooltip>
    )
}

const ArrowSvg = () => (
    <svg width='1em' height='1em' fill='currentColor' viewBox='0 0 1024 1024'>
        {/* <path d='M360.224488 986.001473a37.998527 37.998527 0 0 1 11.151742-26.846785l448.547829-445.656637L371.37623 67.428386A37.998527 37.998527 0 1 1 424.656773 13.734815L900.464415 486.651265a37.998527 37.998527 0 0 1 11.151742 26.846786 37.998527 37.998527 0 0 1-11.151742 26.846785L424.656773 1012.848258a37.998527 37.998527 0 0 1-64.432285-26.846785zM397.809988 456.913288a55.758708 55.758708 0 1 0 55.758708 55.758708A55.758708 55.758708 0 0 0 397.809988 456.913288z m-234.599602 0a55.758708 55.758708 0 1 0 55.758708 55.758708A55.758708 55.758708 0 0 0 163.210386 456.913288z m0 0' /> */}
        <path
            d='M256 924.582524l412.582524-412.582524L256 99.417476V0l512 512L256 1023.857975V924.582524z'
            p-id='44312'
        />
        <path
            d='M256 683.140083l171.140083-171.140083L256 340.859917v-99.417476l270.557559 270.557559L256 782.557559v-99.417476z'
            p-id='44313'
        />
    </svg>
)

export const ArrowIcon = props => <Icon component={ArrowSvg} {...props} />

export function BlackListIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M32 0v1024h731.52v-68.288H100.224V68.288h785.024V380.16H953.6V0z'
                    p-id='2251'
                />
                <path
                    d='M753.6 838.656a167.232 167.232 0 0 1-72.384-17.088l197.952-251.904c25.728 29.312 40 66.88 40.32 105.856a164.48 164.48 0 0 1-165.888 163.136z m-165.568-163.2a164.864 164.864 0 0 1 236.544-147.072l-197.312 251.52a160.768 160.768 0 0 1-39.232-104.384z m165.568-231.68a231.744 231.744 0 1 0 234.112 231.68 233.152 233.152 0 0 0-234.112-231.68z m-529.792 307.2h143.36v-68.288h-143.36v68.224z m0-162.56H426.24V520.256H223.808v68.288z m0-162.432h301.056V357.696H223.808v68.288z m0-162.816h458.752V194.88H223.808v68.288z'
                    p-id='2252'
                />
            </svg>
        )
    }

    return <Icon component={Svg} {...props} />
}

export function TCPIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M556.288 513.984c33.216 0 60.896 5.696 88.576 22.752v-79.648c-27.68-11.392-55.36-17.088-94.112-17.088-60.896 0-110.72 22.784-143.936 62.624-38.752 34.144-55.36 85.376-55.36 147.968s16.608 108.128 49.824 142.272c33.216 34.144 77.504 51.2 132.864 51.2 44.32 0 77.536-11.392 105.216-22.784h5.536v-73.984c-27.68 17.088-60.896 22.784-94.112 22.784s-60.896-11.392-83.04-34.144-33.216-51.232-33.216-91.04c0-39.84 11.072-73.984 33.216-96.736s49.824-34.144 88.576-34.144z m149.472-68.288v392.64h83.04v-136.544h33.216c38.752 0 77.504-11.392 105.184-34.144s44.288-56.896 44.288-96.736c0-85.376-44.288-125.184-138.4-125.184h-127.328z m188.256 125.184c0 39.84-22.144 62.592-71.968 62.592h-33.216v-125.184h33.216c49.824 0 71.968 22.784 71.968 62.592zM152.128 832.672H235.2v-318.688h105.184v-68.288h-293.44v68.288h105.184v318.656z m-110.688 68.288h941.184V992H41.408v-91.04z m53.792-506.304l413.504-245.408 418.816 248.544 47.04-83.84-447.392-265.568-9.248-16.416-9.248 5.472-3.968-2.336-3.936 7.04L48.064 310.816l47.104 83.84z'
                    p-id='5129'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function DNSIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path d='M571.733 682.667h57.6v-294.4H569.6v183.466L452.267 390.4v-2.133h-59.734v294.4h59.734v-185.6L569.6 680.533v2.134zM200.533 320c34.134-55.467 83.2-100.267 140.8-130.133-12.8 36.266-27.733 85.333-38.4 142.933h96c8.534-57.6 23.467-119.467 44.8-179.2h-4.266c23.466-4.267 46.933-6.4 72.533-6.4s49.067 2.133 72.533 8.533c21.334 59.734 34.134 123.734 44.8 179.2h96c-10.666-57.6-25.6-104.533-38.4-140.8 55.467 29.867 102.4 74.667 136.534 128 0 4.267 2.133 6.4 2.133 10.667h121.6C876.8 162.133 708.267 42.667 512 42.667c-198.4 0-366.933 119.466-437.333 290.133h121.6c0-4.267 2.133-10.667 4.266-12.8z m780.8 185.6H804.267V448h170.666c-2.133-21.333-6.4-42.667-10.666-57.6h-217.6v177.067h177.066V627.2H746.667v55.467h204.8c19.2-49.067 25.6-98.134 27.733-142.934v25.6l2.133-59.733zM275.2 622.933V448s-4.267-59.733-59.733-59.733H59.733c-17.066 66.133-32 181.333 10.667 294.4h145.067c2.133 0 59.733 0 59.733-59.734z m-57.6 2.134H100.267v-179.2H217.6v179.2z m471.467 202.666c10.666-27.733 17.066-57.6 23.466-83.2h-93.866C603.733 812.8 586.667 857.6 582.4 870.4c-23.467 4.267-46.933 6.4-72.533 6.4-23.467 0-44.8-2.133-66.134-6.4-2.133-4.267-21.333-49.067-36.266-125.867H313.6c6.4 29.867 14.933 57.6 25.6 87.467-42.667-21.333-78.933-53.333-108.8-87.467h-128c81.067 140.8 232.533 236.8 409.6 236.8 174.933 0 328.533-96 409.6-236.8H789.333c-27.733 34.134-59.733 61.867-100.266 83.2z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function DNSTunIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M525.969 0.543C142.096 0.543 56.323 346.365 56.323 346.365v677.092h69.202V383.733S215.578 93.458 516.374 93.458c300.798 0 384.255 290.275 384.255 290.275v639.724h67.05V346.365c-0.001 0-89.972-345.822-441.71-345.822z m-9.404 130.829c-269.315 0-331.214 259.503-331.214 259.503v632.582h46.553l168.659-299.652h0.654V417.83s17.635-100.137 121.507-100.137c103.982 0 117.746 100.137 117.746 100.137v305.922l168.522 299.705h24.722V390.875c0.001 0-47.833-259.503-317.149-259.503z m-72.638 592.433l-168.658 299.652h492.377L599.069 723.805H443.927z'
                    p-id='2667'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function DDosIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M255.019001 396.63821 256.453578 396.63821 256.453578 395.905157 256.963956 395.648983 256.453578 394.659754 256.453578 158.472719 161.182258 158.472719 161.182258 325.191125 18.290057 396.604711 65.304001 491.174506 254.987471 396.604711 255.019001 396.63821 255.019001 396.63821ZM780.403471 396.63821 781.838049 396.63821 971.521518 491.208007 1018.535462 396.63821 875.643261 325.224624 875.643261 158.504248 780.36997 158.504248 780.36997 394.691284 779.859593 395.680511 780.36997 395.934716 780.36997 396.667769 780.403471 396.63821 780.403471 396.63821ZM516.084528 225.899834 516.722993 225.549073 517.201843 225.868306 517.808778 224.944106 722.356285 106.865368 674.733433 24.351541 530.341628 107.726509 397.050061 19.695076 338.648172 107.69498 515.383003 224.656402 516.116056 225.931365 516.084528 225.899834 516.084528 225.899834ZM256.485107 634.803701 255.01703 634.803701 65.33356 540.233906 18.321586 634.803701 161.213787 706.217287 161.213787 872.935693 256.487077 872.935693 256.487077 636.780186 256.997457 635.759429 256.487077 635.505226 256.487077 634.772173 256.487077 634.803701 256.485107 634.803701ZM780.371941 515.672677C780.371941 370.993166 663.091284 253.714481 518.411774 253.714481 373.732264 253.714481 256.453578 370.993166 256.453578 515.672677 256.453578 660.352189 373.734235 777.632843 518.413745 777.632843 663.093255 777.632843 780.371941 660.352189 780.371941 515.672677L780.371941 515.672677ZM375.359957 515.672677C375.359957 436.66647 439.405568 372.620859 518.380246 372.620859 597.354922 372.620859 661.400533 436.66647 661.400533 515.672677 661.400533 594.678883 597.354922 658.692966 518.380246 658.692966 439.405568 658.692966 375.328428 594.647354 375.328428 515.672677L375.359957 515.672677 375.359957 515.672677ZM781.871548 634.803701 780.403471 634.803701 780.403471 635.536756 779.893092 635.790959 780.403471 636.780186 780.403471 872.967222 875.676762 872.967222 875.676762 706.248817 1018.568963 634.835231 971.555017 540.265434 781.871548 634.835231 781.871548 634.803701 781.871548 634.803701ZM517.201843 805.605135 516.722993 805.924368 516.084528 805.573607 515.351473 806.817039 338.616642 923.809991 397.018531 1011.809894 530.310097 923.778461 674.701905 1007.153429 722.322786 924.639602 517.775279 806.560865 517.168342 805.636665 517.201843 805.605135 517.201843 805.605135Z'
                    p-id='4995'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function MoIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M750.950219 512v204.797334h102.405332a102.405332 102.405332 0 0 0 0-204.797334zM307.229328 273.063112h204.797333v-102.392002a102.405332 102.405332 0 1 0-204.797333 0z m273.063111 682.67111h170.631119V273.063112H68.292439v170.631118h34.139555a170.664445 170.664445 0 0 1 0 341.32889H68.292439v170.711102h170.631119v-34.126224a170.631119 170.631119 0 1 1 341.262237 0z m-273.063111 68.252447H68.292439a68.265778 68.265778 0 0 1-68.292439-68.252447V716.797334H102.405333a102.405332 102.405332 0 0 0 0-204.797334H0V273.063112a68.265778 68.265778 0 0 1 68.265778-68.265778h170.631119v-34.126224a170.631119 170.631119 0 1 1 341.262237 0v34.126224h170.631118a68.265778 68.265778 0 0 1 68.265778 68.265778v170.631118h34.139555a170.664445 170.664445 0 0 1 0 341.32889h-33.979588v170.711102a68.265778 68.265778 0 0 1-68.265778 68.265778H512.026661v-102.405332a102.405332 102.405332 0 1 0-204.797333 0z'
                    p-id='7039'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function TiIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M512 620.03L329.5 931.97h365L512 620.03z'
                    p-id='8407'
                />
                <path
                    d='M382.19 740.86l30.39-51.93C350.81 654.08 309 587.83 309 512c0-111.93 91.07-203 203-203s203 91.07 203 203c0 75.83-41.81 142.08-103.58 176.93l30.39 51.93A264.51 264.51 0 0 0 698 698a263 263 0 1 0-372 0 264.51 264.51 0 0 0 56.19 42.86z'
                    p-id='8408'
                />
                <path
                    d='M921.1 339.17a444 444 0 1 0-630.3 557.91l30.29-51.79a383.59 383.59 0 1 1 381.82 0l30.29 51.79a444.49 444.49 0 0 0 187.9-557.91z'
                    p-id='8409'
                />
                <path
                    d='M512 512m-80 0a80 80 0 1 0 160 0 80 80 0 1 0-160 0Z'
                    p-id='8410'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function HackerIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M745.283105 350.470112l-15.020588 74.943148A69.829756 69.829756 0 0 1 665.386359 479.42346h-68.072027a26.365926 26.365926 0 0 1-24.128817-19.654599l-9.907197-49.695776a17.097904 17.097904 0 1 0-33.39684 6.711326l9.907197 49.695776a60.242146 60.242146 0 0 0 57.525657 46.979287h68.072027a102.907009 102.907009 0 0 0 98.272998-79.896746l15.979349-81.814268zM472.515615 350.470112l-21.731915 108.499781a26.206133 26.206133 0 0 1-23.969024 20.453567H358.582855a69.829756 69.829756 0 0 1-64.876158-53.211233l-3.67525-18.056664a16.93811 16.93811 0 0 0-19.974186-13.422654 17.257697 17.257697 0 0 0-13.742241 19.335013l3.994838 17.417491a103.066802 103.066802 0 0 0 98.272997 79.896745h68.072028a60.242146 60.242146 0 0 0 57.525657-47.13908l23.010262-115.051314zM546.020621 949.056533a17.097904 17.097904 0 0 1-7.989675-31.958699l324.380789-174.015112-57.845244-86.608073a17.097904 17.097904 0 0 1 4.154631-23.170056l165.386264-99.231759c-35.474155-17.577284-105.783292-69.350375-172.736765-73.984386a16.93811 16.93811 0 1 1 2.396902-33.876221c102.267835 7.030914 206.612985 88.206008 211.087203 90.762704a16.93811 16.93811 0 0 1 1.438142 28.443241l-174.174907 105.783292 59.123592 88.685388a16.93811 16.93811 0 0 1 2.237109 13.582447 15.979349 15.979349 0 0 1-8.469055 10.865957L554.170089 946.979217a18.695839 18.695839 0 0 1-8.149468 2.077316z'
                    p-id='614'
                />
                <path
                    d='M564.396873 881.144299a17.417491 17.417491 0 0 1-12.304099-5.113392 17.097904 17.097904 0 0 1 0-24.128817c25.566959-24.448404 53.690613-51.773091 83.731789-81.814268 60.082353-60.082353 95.876095-167.143993 104.984325-220.515019a16.93811 16.93811 0 1 1 33.556633 5.432979c-6.711327 41.386514-40.427753 165.226471-114.41214 239.690238-30.360763 30.20097-58.644212 57.685451-84.211171 82.293648a17.097904 17.097904 0 0 1-11.345337 4.154631zM614.252442 1023.99968a15.979349 15.979349 0 0 1-7.989675-2.077315L128.480227 767.051745a17.097904 17.097904 0 0 1-8.469055-10.865957 15.979349 15.979349 0 0 1 2.396902-13.582447l59.123592-88.525594L7.676347 526.402747A17.097904 17.097904 0 0 1 0.645433 511.382158a17.257697 17.257697 0 0 1 8.469055-13.902033c4.474218-2.556696 108.819368-62.639049 211.246997-69.829756a17.097904 17.097904 0 0 1 18.056664 15.979349 16.93811 16.93811 0 0 1-15.979349 18.216458 504.148467 504.148467 0 0 0-172.576971 53.211233l165.386264 120.164706a17.097904 17.097904 0 0 1 4.154631 23.329849l-57.845244 86.608073 335.566332 179.607885c-37.07209-34.515394-82.293648-77.659637-133.267772-127.834794-72.865832-72.865832-107.381227-197.185169-114.41214-239.690237a17.097904 17.097904 0 0 1 33.556633-5.752566c6.231946 36.273123 38.829819 154.360513 104.984325 220.674812C527.963956 911.984443 623.840052 993.159536 625.278193 993.958504a17.097904 17.097904 0 0 1-11.025751 30.041176zM724.190364 180.449837c1.917522 11.505131 3.67525 22.850469 5.113392 34.036014 164.587297 14.061827 254.391239 62.958636 259.984011 79.896745-7.829881 24.608198-173.855319 47.938048-477.30316 47.938048S42.511328 298.377434 34.681447 274.568203C37.238143 266.738322 77.186516 239.733222 224.356322 221.996145a16.93811 16.93811 0 1 0-4.154631-33.716427C0.645433 215.604405 0.645433 259.547615 0.645433 274.088823c0 101.14928 490.406227 102.267835 511.339174 102.267835s511.339174 19.814393 511.339174-81.175094c0-57.525657-158.515144-103.386389-299.133417-114.731727z'
                    p-id='615'
                />
                <path
                    d='M511.984607 308.124837c-171.298623 0-224.829443-17.097904-226.906758-17.896871a16.778317 16.778317 0 0 1-11.664925-15.979349c0-67.113267 20.61336-160.912046 38.030851-227.705726A62.798842 62.798842 0 0 1 395.335358 4.517202L511.984607 51.176902 628.633856 4.517202a62.639049 62.639049 0 0 1 83.891584 42.025689c14.221621 54.489581 38.030851 156.597622 38.030851 227.545932a16.778317 16.778317 0 0 1-11.664925 15.979349c-2.077315 0.958761-55.608135 18.056665-226.906759 18.056665z m-204.216082-47.938048a1106.729725 1106.729725 0 0 0 204.216082 13.902034 1108.32766 1108.32766 0 0 0 204.216083-13.422653 1116.796715 1116.796715 0 0 0-36.752503-205.654224 27.804068 27.804068 0 0 0-38.190645-18.855632L518.376347 85.372709a17.257697 17.257697 0 0 1-12.623686 0l-123.040989-49.216395a27.963861 27.963861 0 0 0-38.190644 18.855632 1121.750313 1121.750313 0 0 0-36.752503 205.654224z'
                    p-id='616'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function VictimIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M990.204588 328.473532c-10.885984-28.34996-55.441921-155.717778-227.145676-262.905625-20.623971-12.873982-43.703938-23.657966-66.813905-31.405956-5.467992-2.113997-11.317984-2.243997-16.673976 0.35l-94.199866 45.547935-352.505497 51.601926c40.757942-29.997957 86.321877-53.425924 134.487808-68.969901a468.143333 468.143333 0 0 1 75.531893-17.631975 474.039324 474.039324 0 0 1 58.733916-4.939993c5.993991-0.13 12.535982 0.709999 18.201974-1.685998 9.681986-4.093994 14.631979-15.443978 10.987984-25.315964C526.453249 1.315998 515.559265-0.132 504.525281 0.028A513.189268 513.189268 0 0 0 428.599389 6.76999a509.209274 509.209274 0 0 0-140.3538 44.763937c-86.371877 42.16194-161.10177 109.379844-211.695698 191.137727a511.73727 511.73727 0 0 0-52.743925 114.853836c-9.677986 24.671965-14.36598 52.779925-18.229974 78.845888A517.131263 517.131263 0 0 0 0 512.00127c0 24.591965 1.749998 49.18593 5.245993 73.529895 1.403998 9.773986 1.371998 22.505968 9.209986 29.655958 6.90199 6.295991 17.485975 6.96199 25.105965 1.507998l359.665487-173.905752c-9.537986 47.579932 26.877962 93.877866 75.361892 95.755863 47.381932 1.837997 86.887876-39.487944 82.919882-86.743876-2.291997-27.311961-19.049973-52.065926-43.423938-64.547908l144.191794-69.719901 264.245624-38.677945c58.209917 102.155854 75.897892 225.051679 49.415929 339.553516-21.533969 93.113867-71.961897 178.467746-142.619796 242.749654-71.147899 64.727908-161.62177 106.813848-257.063634 119.02183-104.315851 13.343981-211.717698-8.979987-302.037569-62.85991a473.699325 473.699325 0 0 1-118.889831-100.839856 473.069326 473.069326 0 0 1-46.057934-64.883908c-6.351991-10.765985-12.155983-24.141966-27.247961-21.747969-12.409982 1.969997-19.879972 14.915979-15.699978 26.657962 4.271994 12.003983 13.327981 23.785966 20.239971 34.419951a512.18727 512.18727 0 0 0 76.541891 92.051869 514.025267 514.025267 0 0 0 127.967818 88.961873c94.271866 46.127934 202.029712 62.225911 305.677564 45.743935 94.943865-15.099978 184.639737-57.547918 256.839634-120.975828 71.595898-62.89791 125.123822-145.547792 152.703782-236.807662 32.621953-107.947846 28.36996-226.139678-12.087983-331.427527zM176.513748 180.369743l288.847588-42.28194-386.693448 186.975734c23.487967-54.239923 56.861919-103.231853 97.84586-144.693794z m341.245514 278.179603c0 21.789969-18.241974 40.031943-40.031943 40.031943-21.789969 0-40.031943-18.241974-40.031943-40.031943 0-21.789969 18.241974-40.031943 40.031943-40.031943 21.789969 0 40.031943 18.239974 40.031943 40.031943z m127.677818-179.273744a19.873972 19.873972 0 0 0-4.717994 2.281997L43.623938 570.265187c-7.875989-63.971909-2.671996-129.451815 15.525978-191.299727L689.047018 74.395894c59.667915 24.133966 114.403837 60.725913 159.479772 106.681848l-203.08971 98.19786z m132.84581-19.767972l98.14386-47.455932a475.549322 475.549322 0 0 1 22.663968 29.769957l-120.807828 17.685975z'
                    fill=''
                    p-id='6289'
                />
                <path
                    d='M842.762798 383.165454c-31.841955-11.425984-68.273903-1.003999-89.423872 25.337964-20.96397 26.107963-23.301967 63.629909-5.649992 92.105868 17.563975 28.33996 51.789926 43.135938 84.46388 36.321948 36.621948-7.635989 63.701909-40.983942 63.701909-78.381888 0.002-33.431952-21.631969-64.093909-53.091925-75.383892z m-26.969961 115.415835c-21.789969 0-40.031943-18.241974-40.031943-40.031943 0-21.789969 18.241974-40.031943 40.031943-40.031943 21.789969 0 40.031943 18.241974 40.031943 40.031943 0 21.789969-18.241974 40.031943-40.031943 40.031943zM796.164865 737.442949c-34.243951-26.941962-80.879885-37.383947-123.431824-40.613943-49.40993-3.745995-106.627848 1.621998-148.563788 30.557957-17.483975 12.057983-5.377992 40.383942 15.427978 36.079948 8.847987-1.833997 17.139976-9.339987 25.555963-12.867981 9.967986-4.175994 20.417971-7.16199 30.981956-9.393987 47.045933-9.941986 99.551858-6.70199 144.467794 10.893985 8.921987 3.493995 17.655975 7.675989 25.619964 13.033981 7.499989 5.045993 13.477981 12.939982 23.269966 12.937982 10.421985-0.002 19.339972-8.385988 19.981972-18.773974 0.611999-9.929986-6.139991-16.215977-13.309981-21.853968z'
                    fill=''
                    p-id='6290'
                />
                <path
                    d='M524.169253 727.386963c32.679953-22.551968-9.097987 6.273991 0 0z'
                    fill=''
                    p-id='6291'
                />
                <path
                    d='M50.999927 682.351027m-20.015971 0a20.015971 20.015971 0 1 0 40.031943 0 20.015971 20.015971 0 1 0-40.031943 0Z'
                    fill=''
                    p-id='6292'
                />
                <path
                    d='M578.753175 24.971964m-20.015972 0a20.015971 20.015971 0 1 0 40.031943 0 20.015971 20.015971 0 1 0-40.031943 0Z'
                    fill=''
                    p-id='6293'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function AlarmIcon(props) {
    function Svg() {
        return (
            <svg
                viewBox='0 0 1024 1024'
                version='1.1'
                xmlns='http://www.w3.org/2000/svg'
                width='1em'
                height='1em'
                fill='currentColor'
            >
                <path
                    d='M175.787 919.893V604.16c0-189.44 153.6-343.04 343.04-343.04s343.04 153.6 343.04 343.04v315.733h117.76c23.893 0 44.373 20.48 44.373 44.373s-20.48 44.373-44.373 44.373H46.08c-23.893 0-44.373-20.48-44.373-44.373s20.48-44.373 44.373-44.373h129.707z m368.64-520.533l-168.96 278.187h134.827L476.161 885.76l168.96-278.187H510.294l34.133-208.213zM802.133 93.867c15.36 8.533 20.48 29.013 11.947 46.08L752.64 245.76l-58.027-32.427 61.44-105.813c10.24-17.067 30.72-22.187 46.08-13.653zM518.827 15.36c20.48 0 35.84 15.36 35.84 34.133V168.96h-71.68V49.493c0-18.773 15.36-34.133 35.84-34.133zM235.52 93.867c15.36-8.533 35.84-3.413 44.373 11.947l61.44 105.813-58.027 32.427-61.44-105.813c-8.533-15.36-3.413-34.133 13.653-44.373zM27.307 302.08c8.533-15.36 29.013-22.187 44.373-11.947l105.813 61.44-32.427 58.027-105.813-61.44c-15.36-8.533-20.48-29.013-11.947-46.08z m983.04 0c8.533 15.36 3.413 35.84-11.947 46.08L892.587 409.6l-32.427-58.027 105.813-61.44c15.36-8.533 35.84-3.413 44.373 11.947z'
                    fill=''
                    p-id='8088'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

const SunSvg = () => (
    <svg width='1em' height='1em' fill='currentColor' viewBox='0 0 1024 1024'>
        <path
            d='M512.2 906.7c-10 0-18.5-7.5-19.8-17.4v-65.4c1.3-9.9 9.8-17.4 19.8-17.4s18.5 7.5 19.8 17.4v65.4c-1.3 9.9-9.8 17.4-19.8 17.4z m264.6-109.6c-4.4 0-8.6-1.4-12.1-4.1l-46.1-46.1c-6.1-7.9-5.3-19.2 1.7-26.3 3.8-3.8 8.8-5.9 14.1-5.9 4.4 0 8.6 1.4 12.1 4.1l46.1 46.1c6.1 7.9 5.3 19.2-1.7 26.3-3.7 3.8-8.7 5.9-14.1 5.9z m-529.6-0.3c-5.3 0-10.4-2.1-14.1-5.9-7.1-7.1-7.8-18.3-1.7-26.3l46.3-46.3 0.1-0.1c3.8-4.8 9.7-7.7 15.8-7.7 1.5 0 2.9 0.2 4.4 0.5 7.5 1.7 13.5 7.6 15.1 15.1 1.7 7.5-1.2 15.4-7.3 20.1l-0.1 0.1-46.3 46.3c-3.6 2.8-7.8 4.2-12.2 4.2zM512 772.5c-69.6 0-135-27.1-184.2-76.3-49.2-49.2-76.3-114.6-76.3-184.2s27.1-135 76.3-184.2c49.2-49.2 114.6-76.3 184.2-76.3s135 27.1 184.2 76.3c49.2 49.2 76.3 114.6 76.3 184.2 0 34.4-6.7 67.9-19.8 99.7-13.2 31.8-32.2 60.2-56.5 84.5-24.3 24.3-52.7 43.3-84.5 56.5-31.8 13.1-65.3 19.8-99.7 19.8z m0-481.4c-121.8 0-220.9 99.1-220.9 220.9 0 121.8 99.1 220.9 220.9 220.9 121.8 0 220.9-99.1 220.9-220.9 0-121.8-99.1-220.9-220.9-220.9z m380 241c-0.8 0-1.6-0.1-2.4-0.2h-66.1c-0.8 0.1-1.6 0.2-2.4 0.2-6.9 0-13.2-3.5-16.9-9.3-4.1-6.5-4.1-14.9 0-21.4 3.7-5.8 10-9.3 16.9-9.3 0.8 0 1.6 0.1 2.4 0.1h66.1c0.8-0.1 1.6-0.1 2.4-0.1 6.9 0 13.2 3.5 16.9 9.3 4.1 6.5 4.1 14.9 0 21.4-3.7 5.9-10 9.3-16.9 9.3z m-689 0c-0.8 0-1.6-0.1-2.4-0.2h-66.1c-0.8 0.1-1.6 0.2-2.4 0.2-6.9 0-13.2-3.5-16.9-9.3-4.1-6.5-4.1-14.9 0-21.4 3.7-5.8 10-9.3 16.9-9.3 0.8 0 1.6 0.1 2.4 0.1h66.1c0.8-0.1 1.6-0.1 2.4-0.1 6.9 0 13.2 3.5 16.9 9.3 4.1 6.5 4.1 14.9 0 21.4-3.8 5.9-10.1 9.3-16.9 9.3z m90.4-218.7c-6.1 0-12-2.9-15.8-7.7l-0.1-0.1-46.3-46.3c-6.1-7.9-5.3-19.2 1.7-26.3 3.8-3.8 8.8-5.9 14.1-5.9 4.4 0 8.6 1.4 12.1 4.1l46.3 46.3 0.1 0.1c6.1 4.7 8.9 12.6 7.3 20.1-1.7 7.5-7.6 13.5-15.1 15.1-1.3 0.5-2.8 0.6-4.3 0.6z m441.1-3.9c-5.3 0-10.4-2.1-14.1-5.9-7.1-7.1-7.8-18.3-1.7-26.3l46.1-46.1c3.5-2.7 7.7-4.1 12.1-4.1 5.3 0 10.4 2.1 14.1 5.9 7.1 7.1 7.8 18.3 1.7 26.3l-46.1 46.1c-3.5 2.7-7.7 4.1-12.1 4.1z m-222.7-91.9c-10 0-18.5-7.5-19.8-17.4v-65.4c1.3-9.9 9.8-17.4 19.8-17.4s18.5 7.5 19.8 17.4v65.4c-1.2 9.9-9.7 17.4-19.8 17.4z'
            p-id='3420'
        />
    </svg>
)

const DarkSvg = () => (
    <svg width='1em' height='1em' fill='currentColor' viewBox='0 0 1024 1024'>
        <path
            d='M232.704 309.248c26.624 26.624 17.152 72.832 34.56 72.832 17.536 0 6.912-46.336 34.56-72.832 27.648-26.624 72.832-17.152 72.832-34.56 0-17.536-46.208-6.912-72.832-34.56-26.624-27.648-17.152-72.832-34.56-72.832-17.536 0-6.912 46.336-34.56 72.832-27.648 26.624-72.832 17.152-72.832 34.56s46.336 7.936 72.832 34.56zM592.384 128.512c-8.96-1.92-18.176 1.28-23.808 8.448-5.76 7.04-6.912 16.768-3.072 25.088 53.504 115.584 29.184 252.288-60.928 342.4S277.76 618.88 162.176 565.376c-8.32-3.84-18.048-2.56-25.088 3.072-7.04 5.76-10.368 14.976-8.448 23.808 41.984 187.904 215.424 316.672 407.552 302.592 192-14.08 344.832-166.784 358.912-358.912 13.952-192-114.816-365.312-302.72-407.424z m263.552 433.664c-31.104 172.8-181.632 298.368-357.12 297.984v0.768c-151.424-0.512-279.168-110.08-331.392-252.16 131.584 38.528 266.24 17.152 363.136-79.744 96.896-96.896 121.344-224.768 82.816-356.224C777.856 233.6 887.04 389.504 855.936 562.176z'
            p-id='3703'
        />
    </svg>
)

export const SunIcon = props => <Icon component={SunSvg} {...props} />

export const DarkIcon = props => <Icon component={DarkSvg} {...props} />

export function WaveIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M316.16 97.42c10.66-43.08 67.25-44.37 81.16-4.69L547.76 741.31l94.54-334.81c10.4-36.96 56.85-43.07 76.29-12.29l97.06 198.82h164.4c23.97 0 43.4 19.46 43.4 43.46 0 25.36-20.53 45.92-45.86 45.92H789.56c-14.15 0.01-27.37-7.34-35.31-19.62l-60.51-123.52-109.17 386.74c-11.86 41.96-67.17 42.27-80.77 3.13L355.99 292.09 267.46 648.98c-4.44 17.89-18.93 31.07-36.46 33.16l-188.14 0.21c-23.66 0.03-42.86-19.17-42.86-42.87v-4.68c0-23.07 18.68-41.78 41.72-41.78h151.46L316.16 97.42z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function NoWaveIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M316.16 97.42c10.66-43.08 67.25-44.37 81.16-4.69L547.76 741.31l94.54-334.81c10.4-36.96 56.85-43.07 76.29-12.29l97.06 198.82h164.4c23.97 0 43.4 19.46 43.4 43.46 0 25.36-20.53 45.92-45.86 45.92H789.56c-14.15 0.01-27.37-7.34-35.31-19.62l-60.51-123.52-109.17 386.74c-11.86 41.96-67.17 42.27-80.77 3.13L355.99 292.09 267.46 648.98c-4.44 17.89-18.93 31.07-36.46 33.16l-188.14 0.21c-23.66 0.03-42.86-19.17-42.86-42.87v-4.68c0-23.07 18.68-41.78 41.72-41.78h151.46L316.16 97.42z' />
                <rect
                    transform='rotate(135 880.3162841796876,836.7078247070312) '
                    rx='30'
                    height='76.398833'
                    width='338.520808'
                    y='798.508416'
                    x='711.055875'
                />
                <rect
                    transform='rotate(45 880.3162841796877,836.707824707031) '
                    rx='30'
                    height='76.398833'
                    width='338.520808'
                    y='798.508416'
                    x='711.055875'
                />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function NoConfigIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='m459.148214,1003.206185l-37.007198,0a60.364983,60.364983 0 0 1 -60.268864,-56.712336l0,-0.576739l-9.035526,-65.555597a10.669612,10.669612 0 0 0 -6.151842,-8.554918a403.042032,403.042032 0 0 1 -73.437657,-41.717205a10.381242,10.381242 0 0 0 -5.959605,-1.922456a10.092872,10.092872 0 0 0 -3.94103,0.76899l-62.19132,24.99187a62.287438,62.287438 0 0 1 -27.779422,6.536331a61.133973,61.133973 0 0 1 -52.867424,-29.990247l-85.164629,-144.183882a59.115398,59.115398 0 0 1 19.22453,-80.262371l0.672858,0l53.155794,-40.467608a10.76573,10.76573 0 0 0 3.941017,-9.612265a360.84422,360.84422 0 0 1 0,-83.049922a10.76573,10.76573 0 0 0 -3.941017,-9.612252l-52.963556,-40.27537l-0.576739,0a59.211517,59.211517 0 0 1 -19.224517,-79.877869l84.29952,-145.048991a61.61458,61.61458 0 0 1 79.685631,-23.646167l62.479676,25.184121a10.477361,10.477361 0 0 0 3.748792,0.672858a10.669612,10.669612 0 0 0 7.209189,-1.345716a394.102626,394.102626 0 0 1 73.533789,-41.717205a10.669612,10.669612 0 0 0 6.247961,-8.074298l8.939407,-65.55561l0,-0.961228a60.172745,60.172745 0 0 1 60.076613,-56.712322l170.521482,0a60.364983,60.364983 0 0 1 60.749484,56.712322a2.403063,2.403063 0 0 0 0,0.76899l9.131644,65.459478a10.477361,10.477361 0 0 0 6.247961,8.17043a401.696316,401.696316 0 0 1 73.629908,41.621073a9.612265,9.612265 0 0 0 5.959605,1.922456a11.150219,11.150219 0 0 0 4.037149,-0.768977l62.671927,-24.895752l0.48062,0a61.806831,61.806831 0 0 1 80.262358,23.453903l84.684008,144.183895a59.307636,59.307636 0 0 1 -19.224517,80.262371l-0.672858,0l-52.963556,40.275357a10.381242,10.381242 0 0 0 -3.941017,9.612265c1.153465,9.612252 1.922442,19.993494 2.210812,29.990247l-72.380311,0a299.710247,299.710247 0 0 0 -5.190614,-45.177615l-2.691433,-14.322272a10.57348,10.57348 0 0 1 3.844898,-9.612252l76.225222,-57.673563a10.285123,10.285123 0 0 0 2.691433,-13.457163l-66.612957,-114.482005a10.285123,10.285123 0 0 0 -8.843288,-5.094496a9.612265,9.612265 0 0 0 -4.133268,0.865096l-88.432787,37.391687a10.957981,10.957981 0 0 1 -4.133268,0.76899a10.57348,10.57348 0 0 1 -6.632463,-2.403063l-11.919196,-10.092872a319.319252,319.319252 0 0 0 -99.390769,-56.231728l-14.706747,-5.190614a10.57348,10.57348 0 0 1 -6.728582,-8.362667l-13.168793,-93.911771a10.285123,10.285123 0 0 0 -10.189004,-8.939407l-131.880198,0a10.188991,10.188991 0 0 0 -10.188991,8.939407l-13.072675,94.104022a10.381242,10.381242 0 0 1 -7.016951,8.747156l-14.418391,5.286733a321.818447,321.818447 0 0 0 -99.583006,56.423966l-11.919196,9.612265a10.669612,10.669612 0 0 1 -6.632463,2.306944a9.612265,9.612265 0 0 1 -3.844898,-0.76899l-89.490134,-36.04597a10.57348,10.57348 0 0 0 -3.748779,-0.672858a10.285123,10.285123 0 0 0 -8.939407,5.094496l-65.651729,111.790585a10.57348,10.57348 0 0 0 2.691433,13.553282l76.225222,57.67355a10.285123,10.285123 0 0 1 3.941017,10.092872l-2.787551,15.571869a323.45252,323.45252 0 0 0 -5.478984,56.327834a332.968666,332.968666 0 0 0 5.382865,56.520085l2.595301,14.898998a9.612265,9.612265 0 0 1 -3.941017,9.612265l-76.129103,57.67355a10.477361,10.477361 0 0 0 -2.595301,13.457163l66.805195,113.52079a10.285123,10.285123 0 0 0 8.939407,5.190614a10.381242,10.381242 0 0 0 4.133268,-0.384488l88.336669,-37.487806a10.285123,10.285123 0 0 1 3.941017,-0.961228a9.612265,9.612265 0 0 1 6.536344,2.403063l12.111447,9.612252a321.722315,321.722315 0 0 0 99.486888,56.231728l14.033889,5.767354a10.669612,10.669612 0 0 1 6.728582,8.362667l12.976556,94.104009a10.188991,10.188991 0 0 0 10.188991,8.939407l18.16717,0l0,70.842344l0,0.096132zm0,-290.001863a204.933366,204.933366 0 1 1 255.686097,-210.123994l-72.284192,0a135.628977,135.628977 0 1 0 -183.401906,136.301835l0,73.822159z' />
                <path d='m883.642505,597.651294l55.140644,55.140644l-344.482876,344.385444l-55.14063,-55.043212l344.482862,-344.482876z' />
                <path d='m938.783148,942.134169l-55.140644,55.14063l-344.385444,-344.482862l55.043212,-55.140644l344.482876,344.482876z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function NavIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M605.848435 958.708971c-0.36225 0-0.724501-0.008186-1.088798-0.026606-9.098215-0.468674-16.872273-6.712889-19.288299-15.495926L476.898551 548.458353 80.07064 437.83486c-8.760524-2.442632-14.976086-10.218736-15.427364-19.304671-0.451278-9.083889 4.965082-17.437138 13.44215-20.73423L929.251056 66.728774c7.80885-3.038196 16.669658-1.174756 22.597671 4.750187 5.922896 5.92392 7.788383 14.789844 4.751211 22.597671L625.531729 945.241238C622.361527 953.390849 614.518908 958.708971 605.848435 958.708971zM152.537092 414.172951l347.232352 96.79658c7.148817 1.9934 12.726859 7.591909 14.696724 14.746866l94.821599 344.730369 290.525839-746.93166L152.537092 414.172951z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function DataAnalysis(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M361.39008 444.68224l-47.20128 78.69952c-0.78848-0.08192-1.73056-0.08192-2.51392-0.08192-4.5568 0-8.87808 0.71168-12.95872 1.96608l-32.44032-32.44032a43.776 43.776 0 0 0 1.96608-12.95872c0-23.95648-19.4816-43.43296-43.43808-43.43296s-43.42784 19.4816-43.42784 43.43296c0 8.33024 2.3552 16.0256 6.36416 22.62528l-47.2832 78.69952c-0.78848-0.08192-1.73056-0.08192-2.51392-0.08192-23.95648 0-43.43296 19.47648-43.43296 43.42784 0 23.9616 19.4816 43.43296 43.43296 43.43296 23.95136 0 43.43296-19.4816 43.43296-43.43296a43.4432 43.4432 0 0 0-6.35904-22.62016l47.27808-78.78656c0.78336 0.08704 1.73056 0.08704 2.5088 0.08704 4.56192 0 8.87808-0.71168 12.96384-1.96096l32.44032 32.44032a43.70944 43.70944 0 0 0-1.96608 12.94848c0 23.9616 19.4816 43.43296 43.43296 43.43296s43.35616-19.47648 43.35616-43.43296c0-8.32512-2.3552-16.02048-6.35904-22.61504 0 0 49.00352-78.62272 49.79712-78.62272l-37.07904-20.72576z' />
                <path d='M363.71968 521.50784l47.2832-78.78656a43.91936 43.91936 0 0 0 15.46752-1.87904l131.67104 131.6608a43.8272 43.8272 0 0 0-1.9712 12.95872c0 23.9616 19.4816 43.43296 43.42784 43.43296 23.96672 0 43.3664-19.48672 43.3664-43.43296a43.3152 43.3152 0 0 0-6.36416-22.62016l47.27808-78.70464c0.78848 0.08192 1.73056 0.08192 2.51904 0.08192 23.95648 0 43.42784-19.4816 43.42784-43.43296 0-23.95136-19.4816-43.35616-43.42784-43.35616-23.9616 0-43.43296 19.4816-43.43296 43.43296 0 8.32512 2.3552 16.0256 6.35392 22.62016l-47.20128 78.70976c-0.78848-0.08704-1.73056-0.08704-2.51904-0.08704-4.55168 0-8.86784 0.70656-12.94848 1.96608L454.9888 412.416a43.94496 43.94496 0 0 0 1.96096-12.95872c0-23.95648-19.4816-43.43296-43.43296-43.43296-23.95136 0-43.43296 19.4816-43.43296 43.43296a43.4176 43.4176 0 0 0 6.36416 22.62528s-49.01376 78.61248-49.80224 78.61248l37.07392 20.8128z' />
                <path d='M780.20608 704.46592l139.57632 140.04736c12.88192 12.20096 12.88192 32.93184 0.45056 45.35808-12.42112 12.90752-33.152 13.35296-45.60384 0.45056l-140.49792-140.26752c-27.17184 22.57408-57.5744 41.2416-90.74688 54.81984a350.39744 350.39744 0 0 1-135.67488 27.20256c-103.42912 0-202.0096-45.85984-269.27616-125.33248-33.86368-39.59808 17.51552-76.68736 56.20736-34.06848 54.8352 60.11904 131.77344 94.19776 213.0688 94.19776 39.38304 0 76.70272-7.82336 110.55104-21.87264a286.2336 286.2336 0 0 0 93.52704-62.18752l0.47104-0.47104c26.49088-26.72128 48.14848-58.48064 62.87872-93.97248 13.81888-33.39776 21.66272-70.9376 21.66272-110.336a290.7904 290.7904 0 0 0-21.21728-108.9536l-0.45056-1.3824a287.0784 287.0784 0 0 0-61.27616-92.37504l-1.6128-1.59744c-26.95168-26.49088-58.74176-48.13312-93.98272-62.44352-33.60256-14.49472-71.15264-22.08256-110.55104-22.08256-117.69344 0-219.03872 71.85408-263.74656 172.49792-5.5296 11.99104-16.58368 19.12832-29.72672 19.12832h-0.20992c-11.52 0-20.96128-5.53984-27.19232-14.73024-6.21056-9.46176-6.912-20.51584-2.29376-31.12448C240.27648 209.92 367.43168 124.22656 507.6992 124.22656c47.67232 0 93.53216 9.6768 135.68 27.17184 43.30496 17.75104 82.45248 44.24704 114.69824 76.47744l1.8176 1.63328c31.80544 32.4608 57.14432 70.7072 74.87488 113.1008l0.68608 1.82272c17.04448 41.48224 26.27072 86.13888 26.27072 133.59616a350.47424 350.47424 0 0 1-26.9568 134.97856 356.71552 356.71552 0 0 1-54.56384 91.45856z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function MiningIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M395.776 906.24c-71.168 0-142.336-27.136-196.608-81.408C146.432 772.096 117.76 702.464 117.76 628.224s28.672-143.872 81.408-196.608l94.208-94.208c22.016-22.016 51.712-34.304 82.944-34.304s60.928 12.288 82.944 34.304l22.528 22.528 74.752-74.752-25.088-25.088c-15.872-15.872-24.576-36.864-24.576-58.88 0-22.528 8.704-43.008 24.576-58.88 32.256-32.768 85.504-32.768 117.76 0l231.936 231.936c32.256 32.768 32.256 85.504 0 117.76-15.872 15.872-36.864 24.576-58.88 24.576-22.528 0-43.008-8.704-58.88-24.576l-25.088-25.088-74.752 74.752 22.528 22.528c45.568 45.568 45.568 120.32 0 166.4l-94.208 94.208C538.112 879.104 466.944 906.24 395.776 906.24zM376.832 369.664c-13.824 0-26.624 5.12-35.84 14.848L246.272 478.72c-39.936 39.936-61.952 93.184-61.952 149.504s22.016 109.568 61.952 149.504c82.432 82.432 216.576 82.432 299.008 0l94.208-94.208c19.968-19.968 19.968-52.224 0-72.192l-46.08-46.08c-12.8-12.8-12.8-34.304 0-47.104l121.856-121.856c12.8-12.8 34.304-12.8 47.104 0l48.64 48.64c4.096 4.096 9.216 5.12 11.776 5.12 2.56 0 7.68-0.512 11.776-5.12 6.656-6.656 6.656-17.408 0-23.552L602.624 189.44c-4.096-4.096-9.216-5.12-11.776-5.12s-7.68 0.512-11.776 5.12c-4.096 4.096-5.12 9.216-5.12 11.776s0.512 7.68 5.12 11.776l48.64 48.64c12.8 12.8 12.8 34.304 0 47.104L505.856 430.08c-12.8 12.8-34.304 12.8-47.104 0l-46.08-46.08c-9.728-9.216-22.528-14.336-35.84-14.336z' />
                <path d='M318.464 738.304c-8.704 0-16.896-3.072-23.552-9.728-12.8-12.8-12.8-34.304 0-47.104l146.432-146.432c12.8-12.8 34.304-12.8 47.104 0 12.8 12.8 12.8 34.304 0 47.104l-146.432 146.432c-6.656 6.656-15.36 9.728-23.552 9.728z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function IpScanIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M344.283429 249.563429a18.285714 18.285714 0 1 1 36.571428 0 18.285714 18.285714 0 0 1-36.571428 0z m414.793142 200.192a47.396571 47.396571 0 1 1 94.72 0 47.396571 47.396571 0 0 1-94.72 0zM489.910857 293.302857a36.278857 36.278857 0 1 1 72.557714 0 36.278857 36.278857 0 0 1-72.484571 0z m47.396572-127.341714a29.110857 29.110857 0 1 1 58.148571 0 29.110857 29.110857 0 0 1-58.148571 0z m167.277714 420.352a16.310857 16.310857 0 1 1 32.621714 0 16.310857 16.310857 0 0 1-32.621714 0z m-192.731429 437.394286a508.196571 508.196571 0 0 1-361.325714-150.674286A508.196571 508.196571 0 0 1 0 511.926857c0-135.899429 53.540571-264.265143 150.601143-361.325714A508.196571 508.196571 0 0 1 511.853714 0a514.194286 514.194286 0 0 1 361.910857 149.942857l29.037715 29.037714-364.178286 359.936-57.490286-58.075428 304.420572-301.202286a433.005714 433.005714 0 0 0-273.700572-98.304 431.104 431.104 0 0 0-430.518857 430.518857 431.104 431.104 0 0 0 430.518857 430.518857 430.884571 430.884571 0 0 0 418.742857-530.505142l79.36-18.944a508.196571 508.196571 0 0 1-136.557714 480.182857 509.513143 509.513143 0 0 1-361.545143 150.601143z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function IcmpTunIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M512 38.4C250.88 38.4 38.4 252.416 38.4 515.584v343.552c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-343.552c0-248.832 201.216-451.584 448-451.584s448 202.24 448 451.584v343.552c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-343.552C985.6 252.416 773.12 38.4 512 38.4z' />
                <path d='M512 126.976c-212.992 0-386.048 174.592-386.048 388.608v262.656c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-262.656c0-200.192 161.792-363.008 360.448-363.008s360.448 162.816 360.448 363.008v262.656c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-262.656c0-214.528-173.056-388.608-386.048-388.608z' />
                <path d='M512 228.864c-158.72 0-288.256 130.048-288.256 290.304v177.152c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-177.152c0-145.92 117.76-264.704 262.656-264.704s262.656 118.784 262.656 264.704v177.152c0 7.168 5.632 12.8 12.8 12.8s12.8-5.632 12.8-12.8v-177.152c0-160.256-129.536-290.304-288.256-290.304z' />
                <path d='M981.504 963.584l-285.184-279.04v-171.008c0-102.4-82.944-185.856-184.32-185.856-101.888 0-184.832 83.456-184.832 185.856v171.008L41.984 963.584c-5.12 5.12-5.12 12.8 0 17.92 2.56 2.56 5.632 4.096 9.216 4.096 3.072 0 6.656-1.024 8.704-3.584l285.184-279.04h333.312l285.184 279.04c2.56 2.56 5.632 3.584 8.704 3.584 3.072 0 6.656-1.536 9.216-4.096 5.632-4.608 5.12-12.8 0-17.92zM512 353.28c87.552 0 158.72 71.68 158.72 160.256v163.84H352.768v-163.84C352.768 424.96 424.448 353.28 512 353.28z' />
                <path d='M579.072 809.984c7.168 0 12.8-5.632 12.8-12.8s-5.632-12.8-12.8-12.8H444.928c-7.168 0-12.8 5.632-12.8 12.8s5.632 12.8 12.8 12.8h134.144zM611.84 880.128h-199.68c-7.168 0-12.8 5.632-12.8 12.8s5.632 12.8 12.8 12.8h199.68c7.168 0 12.8-5.632 12.8-12.8s-5.632-12.8-12.8-12.8z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function CapIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M96 64v896h832V64H96z m447.8 64h174v83.2h-174V128z m-237.7 0h174v83.2h-174V128z m-146.4 0h82.8v83.2h-82.8V128z m704.6 768H159.7V275.2h704.7V896z m0-684.8h-82.8V128h82.8v83.2z' />
                <path d='M333.5 691.2l-52.9 53.2 45 45.3 52.9-53.2c23.8 17.2 51.7 29.2 81.9 34.2v75.2H524v-75.2c30.1-5 58-16.9 81.8-34.1l52.9 53.2 45-45.3-52.9-53.2c17.1-24 29-52 34-82.3h94.6v-64h-94.6c-5-30.3-16.8-58.3-34-82.2l52.9-53.2-45-45.3-52.9 53.2c-23.8-17.2-51.7-29.2-81.8-34.1v-75.2h-63.7v75.2c-30.1 5-58 16.9-81.8 34.1l-52.9-53.2-45 45.3 52.9 53.2c-17.1 24-29 51.9-34 82.2h-55v64h55c5 30.3 16.9 58.3 34 82.2z m29.9-141.4c12.5-59.9 65.5-105 128.8-105 72.6 0 131.6 59.3 131.6 132.3 0 72.9-59 132.3-131.6 132.3-63.3 0-116.3-45.1-128.8-105v-54.6z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

export function UrlIcon(props) {
    function Svg() {
        return (
            <svg
                width='1em'
                height='1em'
                fill='currentColor'
                viewBox='0 0 1024 1024'
            >
                <path d='M938.666667 853.333333v85.333334H682.666667V512h85.333333v341.333333z m-85.333334-519.253333V469.333333h85.333334v-170.666666L725.333333 85.333333H85.333333v384h85.333334V170.666667h519.253333zM599.04 733.653333L640 938.666667h-85.333333l-34.133334-170.666667H469.333333v170.666667h-85.333333V512h128a128 128 0 0 1 87.04 221.653333zM512 682.666667a42.666667 42.666667 0 0 0 0-85.333334h-42.666667v85.333334z m-256-170.666667v298.666667a42.666667 42.666667 0 0 1-85.333333 0V512H85.333333v298.666667a128 128 0 0 0 256 0V512z' />
            </svg>
        )
    }
    return <Icon component={Svg} {...props} />
}

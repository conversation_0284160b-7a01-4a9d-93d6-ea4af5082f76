const darkTheme = {
    themeName: 'darkTheme',
    color: [
        '#2251f7',
        '#5eff5a',
        '#ffa63f',

        '#e323ff',
        '#02a4ff',
        '#17eb8e',
        '#ff7d4d',
        '#991bfa',
        '#e323ff',
    ],
    backgroundColor: 'rgba(0,0,0,0)',
    textStyle: {},
    title: {
        textStyle: {
            color: 'rgba(255, 255, 255, 0.8)',
        },
        subtextStyle: {
            color: '#9999bf',
        },
    },
    line: {
        itemStyle: {
            borderWidth: 1,
        },
        lineStyle: {
            width: 2,
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
    },
    radar: {
        itemStyle: {
            borderWidth: 1,
        },
        lineStyle: {
            width: 2,
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
    },
    bar: {
        itemStyle: {
            barBorderWidth: '',
            barBorderColor: '#9999bf',
        },
    },
    pie: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    scatter: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },


    },
    boxplot: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    parallel: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    sankey: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    funnel: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    gauge: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
    },
    candlestick: {
        itemStyle: {
            color: '#eb5454',
            color0: '#47b262',
            borderColor: '#eb5454',
            borderColor0: '#47b262',
            borderWidth: 1,
        },
    },
    graph: {
        itemStyle: {
            borderWidth: '0',
            borderColor: '#9999bf',
        },
        lineStyle: {
            width: 1,
            color: '#e323ff',
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
        color: [
            '#023aff',
            '#7d40ff',
            '#e323ff',
            '#ff2d2e',
            '#ffa63f',
            '#01f1e3',
        ],
        label: {
            color: '#ffffff',
        },
    },
    map: {
        itemStyle: {
            normal: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5,
            },
            emphasis: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1,
            },
        },
        label: {
            normal: {
                textStyle: {
                    color: '#000',
                },
            },
            emphasis: {
                textStyle: {
                    color: 'rgb(100,0,0)',
                },
            },
        },
    },
    geo: {
        itemStyle: {
            normal: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5,
            },
            emphasis: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1,
            },
        },
        label: {
            normal: {
                textStyle: {
                    color: '#000',
                },
            },

            emphasis: {
                textStyle: {
                    color: 'rgb(100,0,0)',
                },
            },
        },
    },
    categoryAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#9999bf',
            },
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#9999bf',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#9999bf',
            },
        },
        splitLine: {
            show: false,
            lineStyle: {
                color: ['#E0E6F1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    valueAxis: {
        axisLine: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisTick: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#9999bf',
            },
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['rgba(90, 90, 137, .3)'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    logAxis: {
        axisLine: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisTick: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#9999bf',
            },
        },
        splitLine: {
            show: true,


            lineStyle: {
                color: ['#9999bf'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {

                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    timeAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#9999bf',
            },
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#9999bf',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#9999bf',
            },
        },
        splitLine: {
            show: false,
            lineStyle: {
                color: ['#E0E6F1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    toolbox: {
        iconStyle: {
            normal: {

                borderColor: '#999999',
            },
            emphasis: {
                borderColor: '#666',
            },
        },
    },
    legend: {
        textStyle: {
            color: '#9999bf',
        },
    },
    tooltip: {
        axisPointer: {
            lineStyle: {
                color: '#ccc',
                width: 1,
            },
            crossStyle: {
                color: '#ccc',
                width: 1,
            },
        },
    },
    timeline: {
        lineStyle: {
            color: '#DAE1F5',
            width: 2,
        },
        itemStyle: {
            normal: {
                color: '#A4B1D7',
                borderWidth: 1,
            },
            emphasis: {
                color: '#FFF',
            },
        },
        controlStyle: {
            normal: {
                color: '#A4B1D7',
                borderColor: '#A4B1D7',
                borderWidth: 1,
            },
            emphasis: {
                color: '#A4B1D7',
                borderColor: '#A4B1D7',
                borderWidth: 1,
            },
        },
        checkpointStyle: {
            color: '#316bf3',
            borderColor: 'fff',
        },
        label: {
            normal: {
                textStyle: {
                    color: '#A4B1D7',
                },
            },
            emphasis: {
                textStyle: {
                    color: '#A4B1D7',
                },
            },
        },
    },
    visualMap: {
        color: ['#7d40ff', '#02a4ff'],
    },
    dataZoom: {
        handleSize: 'undefined%',
        textStyle: {},
    },
    markPoint: {
        label: {
            color: '#ffffff',
        },
        emphasis: {
            label: {
                color: '#ffffff',
            },
        },
    },
    tree: {

        label: {
            color: '#9999bf',
        },
        lineStyle: {
            color: '#9999bf',
        },
    },
}
export default darkTheme

const lightTheme = {
    themeName: 'lightTheme',
    color: [
        '#3a65ff',
        '#5eff5a',
        '#ffba69',
        '#8676ff',
        '#02a4ff',
        '#17eb8e',
        '#ff7d4d',
        '#991bfa',
        '#e323ff',
    ],
    backgroundColor: 'rgba(0,0,0,0)',
    textStyle: {},
    title: {
        textStyle: {
            color: '#3a3a74',
        },
        subtextStyle: {
            color: '#84859f',
        },
    },
    line: {
        itemStyle: {
            borderWidth: 1,
        },
        lineStyle: {
            width: 2,
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
    },
    radar: {
        itemStyle: {
            borderWidth: 1,
        },
        lineStyle: {
            width: 2,
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
    },
    bar: {
        itemStyle: {
            barBorderWidth: 0,
            barBorderColor: '#ccc',
        },
    },
    pie: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    scatter: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    boxplot: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    parallel: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    sankey: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    funnel: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    gauge: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
    },
    candlestick: {
        itemStyle: {
            color: '#eb5454',
            color0: '#47b262',
            borderColor: '#eb5454',
            borderColor0: '#47b262',
            borderWidth: 1,
        },
    },
    graph: {
        itemStyle: {
            borderWidth: 0,
            borderColor: '#ccc',
        },
        lineStyle: {
            width: 1,
            color: '#aaaaaa',
        },
        symbolSize: 4,
        symbol: 'emptyCircle',
        smooth: false,
        color: [
            '#3a65ff',
            '#5eff5a',
            '#ffba69',
            '#8676ff',
            '#02a4ff',
            '#17eb8e',
            '#ff7d4d',
            '#991bfa',
            '#e323ff',
        ],
        label: {
            color: '#eee',
        },
    },
    map: {
        itemStyle: {
            normal: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5,
            },
            emphasis: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1,
            },
        },
        label: {
            normal: {
                textStyle: {
                    color: '#000',
                },
            },
            emphasis: {


                textStyle: {
                    color: 'rgb(100,0,0)',
                },
            },
        },
    },
    geo: {
        itemStyle: {
            normal: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5,
            },
            emphasis: {
                areaColor: 'rgba(255,215,0,0.8)',
                borderColor: '#444',
                borderWidth: 1,
            },
        },
        label: {
            normal: {
                textStyle: {
                    color: '#000',
                },
            },
            emphasis: {
                textStyle: {
                    color: 'rgb(100,0,0)',
                },
            },
        },
    },
    categoryAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#84859f',
            },
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#84859f',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#84859f',
            },
        },
        splitLine: {
            show: false,
            lineStyle: {
                color: ['#E0E6F1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    valueAxis: {
        axisLine: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisTick: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#84859f',
            },
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['#e0e6f1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    logAxis: {
        axisLine: {
            show: false,
            lineStyle: {
                color: '#6E7079',
            },
        },
        axisTick: {
            show: false,

            lineStyle: {
                color: '#6E7079',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#84859f',
            },
        },
        splitLine: {
            show: true,
            lineStyle: {
                color: ['#e0e6f1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    timeAxis: {
        axisLine: {
            show: true,
            lineStyle: {
                color: '#84859f',
            },
        },
        axisTick: {
            show: true,
            lineStyle: {
                color: '#84859f',
            },
        },
        axisLabel: {
            show: true,
            textStyle: {
                color: '#84859f',
            },
        },
        splitLine: {
            show: false,
            lineStyle: {
                color: ['#E0E6F1'],
            },
        },
        splitArea: {
            show: false,
            areaStyle: {
                color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)'],
            },
        },
    },
    toolbox: {
        iconStyle: {
            normal: {
                borderColor: '#999',
            },
            emphasis: {
                borderColor: '#666',
            },
        },
    },
    legend: {
        textStyle: {
            color: '#84859f',
        },
    },
    tooltip: {
        axisPointer: {
            lineStyle: {
                color: '#ccc',
                width: 1,
            },
            crossStyle: {
                color: '#ccc',
                width: 1,
            },
        },
    },
    timeline: {
        lineStyle: {
            color: '#DAE1F5',
            width: 2,
        },
        itemStyle: {
            normal: {
                color: '#A4B1D7',
                borderWidth: 1,
            },
            emphasis: {
                color: '#FFF',
            },
        },
        controlStyle: {
            normal: {
                color: '#A4B1D7',
                borderColor: '#A4B1D7',
                borderWidth: 1,
            },
            emphasis: {
                color: '#A4B1D7',
                borderColor: '#A4B1D7',
                borderWidth: 1,
            },
        },
        checkpointStyle: {
            color: '#316bf3',
            borderColor: 'fff',
        },
        label: {
            normal: {
                textStyle: {
                    color: '#A4B1D7',
                },
            },
            emphasis: {
                textStyle: {
                    color: '#A4B1D7',
                },
            },
        },
    },
    visualMap: {
        color: ['#7517f8', '#02a4ff'],
    },
    dataZoom: {
        handleSize: 'undefined%',
        textStyle: {},
    },
    markPoint: {
        label: {
            color: '#eee',
        },
        emphasis: {
            label: {
                color: '#eee',
            },
        },
    },
    tree: {
        label: {
            color: '#666666',
        },
        lineStyle: {
            color: '#DBDFF1',
        },
    },
}
export default lightTheme

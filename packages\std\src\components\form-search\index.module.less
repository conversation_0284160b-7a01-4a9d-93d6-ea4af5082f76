@content-max-width: 900px;
.search-form {
    display: flex;
    flex-flow: column;
    align-items: center;
    width: 100%;
    :global {
        .search-value {
            max-width: @content-max-width;
            width: 80%;
            .ant-input-group {
                display: flex;
            }
            .ant-form-item {
                margin-right: 0;
                margin-bottom: 0;
            }
            .search-value-type {
                min-width: 70px;
            }
            .search-value-single {
                flex: 1;
            }
            .search-value-complex {
                flex: 1;
                display: inline-flex;
                // align-items: center;
                .ant-form-item {
                    &:nth-child(1) {
                        flex: 1;
                    }
                    &:nth-child(2) {
                        width: 30px;
                        text-align: center;
                        font-size: 20px;
                    }
                    &:nth-child(3) {
                        width: 150px;
                    }
                }
            }
        }
        .collapse-condition {
            justify-content: center;
            width: 80%;
            max-width: @content-max-width;
            .ant-collapse-header {
                padding: 0;
            }
            .ant-collapse-content-box {
                padding: 0 !important;
            }
            .search-condition {
                .ant-form-item {
                    margin-top: 20px;
                    margin-bottom: 0;
                }
            }
        }
        .search-extra {
            display: flex;
            margin-right: -100px;
            line-height: 40px;
            .search-tip {
                font-size: 18px;
                // align-self: center;
                margin-left: 20px;
                line-height: inherit;
                color: var(--text-desc);
            }
            .collapse-control {
                // align-self: center;
                margin-left: 20px;
                font-size: 12px;
                color: var(--text-link);
                opacity: 0.8;
                cursor: pointer;
                .anticon {
                    margin-right: 5px;
                }
            }
        }
        .ant-checkbox-group {
            label.ant-checkbox-wrapper {
                margin: 5px 8px 5px 0;
            }
        }
        .input-limit {
            width: 60px;
        }
    }
}
.search-form-simple {
    width: 100%;
    position: relative;
    :global {
        .search-value {
            display: flex;
            align-items: center;
            .ant-form-item {
                margin-bottom: 0 !important;
                margin-right: 0;
            }
            .search-extra {
                margin: 0 10px;
            }
            .search-container {
                border: var(--border-default) 1px solid;
                height: 30px;
                border-radius: 4px;
                background-color: var(--bg-body);
                width: 100%;
                display: flex;
                &:hover,
                &:focus {
                    border-color: var(--border-active);
                    input {
                        border-color: var(--border-active) !important;
                    }
                    .search-value-complex {
                        .ant-form-item {
                            &:nth-child(2) {
                                border-color: var(--border-active) !important;
                            }
                        }
                    }
                }
                .search-type-container {
                    width: 80px;
                    .ant-select-selector {
                        border: none;
                        background: none;
                        &:hover,
                        &:focus {
                            border: none;
                            box-shadow: none;
                        }
                    }
                }
                input {
                    height: 30px;
                    line-height: 30px;
                    border: none;
                    border-left: solid 1px var(--border-default);
                    border-radius: 0;
                    background: none;
                    &:hover,
                    &:focus {
                        box-shadow: none;
                    }
                }
                .ant-form-item-explain.ant-form-item-explain-error {
                    display: none !important;
                }
                .search-input-container {
                    flex: 1;
                    .search-value-single {
                        width: 100%;
                    }
                    .search-value-complex {
                        width: 100%;
                        display: inline-flex;
                        align-items: center;
                        .ant-form-item {
                            &:nth-child(1) {
                                flex: 1;
                            }
                            &:nth-child(2) {
                                height: 30px;
                                width: 20px;
                                text-align: center;
                                font-size: 20px;
                                background: var(--bg-default);
                                border-left: solid 1px var(--border-default);
                            }
                            &:nth-child(3) {
                                min-width: 80px;
                                width: 30%;
                            }
                        }
                    }
                }
                .anticon-search {
                    font-size: 14px;
                    cursor: pointer;
                    padding: 0 10px;
                }
            }
        }
        .search-condition-container {
            background: var(--bg-default);
            border-radius: 8px;
            position: absolute;
            top: 44px;
            padding: 20px;
            box-shadow: var(--shadow-2-down);
            min-width: 400px;
            right: 0;
            left: 0;
            z-index: 2;
            &.hidden {
                top: -999px;
            }
            .close {
                position: absolute;
                top: -10px;
                right: 10px;
                font-size: 14px;
                cursor: pointer;
            }
            .feature-select {
                width: 312px;
            }
            .ant-form-item {
                margin-bottom: 10px !important;
            }
        }
        .input-limit {
            width: 60px;
        }
    }
}

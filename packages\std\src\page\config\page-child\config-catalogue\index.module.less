.configCatalogue {
    :global {
        .input-box {
            border-bottom: solid 1px var(--border-default-weight);
            margin-bottom: 10px;
        }
        .page-item {
            padding: 15px 0;
            .page-item-title {
                font-size: 1.25rem;
                font-weight: bold;
                padding: 5px 0;
                color: var(--text-title);
            }
            .page-nav-type-item {
                padding: 10px 0;
                .page-nav-type-item-title {
                    font-size: 0.875rem;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: var(--text-title);
                }
                .page-nav-node-item {
                    width: 100%;
                    height: 100px;
                    border-radius: 8px;
                    box-shadow: var(--shadow-1-down);
                }
            }
        }
        .ant-card {
            border-color: var(--border-default);
            .ant-card-head-title {
                padding: 5px 0;
                font-size: 0.75rem;
                color: var(--text-main);
            }
            .ant-card-head {
                border-bottom: 1px solid var(--border-default);
            }
        }
        .card-content {


            display: flex;
            justify-content: space-between;
            color: var(--text-main);
            cursor: pointer;
            .anticon {
                margin-right: 4px;
            }
        }
    }
}

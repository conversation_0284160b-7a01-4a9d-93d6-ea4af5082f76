.event-detail-table {
    :global {
        .table-top-content {
            width: 100%;
            padding: 10px 30px;
            min-height: 50px;
        }
        .payload_content {
            width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
.flow-chart {
    :global {
        min-height: 100px;
        padding-bottom: 20px;
        .label-line {
            stroke: var(--color-lightgray);
            stroke-width: 1;
        }
        .dashed-line {
            stroke-width: 1px;
            stroke: var(--border-default);
            stroke-dasharray: 5, 2;
        }
        .xAxis-label {
            text-anchor: middle;
            font-size: 10px;

            fill: var(--text-desc);
        }
        .label-y {
            font-size: 10px;
            color: var(--text-title);
            .domain {
                stroke: var(--color-lightgray);
            }
        }
        .blue-line {
            stroke: var(--text-blue);
            stroke-dasharray: 2, 1;
            stroke-width: 1px;
        }
        .red-line {
            stroke: var(--text-red);
            stroke-dasharray: 2, 1;
            stroke-width: 1px;
        }
        .blue-path {
            stroke: var(--text-blue);
            fill: rgba(var(--color-rgb-blue), 0.2);
        }
        .red-path {
            stroke: var(--text-red);
            fill: rgba(var(--color-rgb-red), 0.2);
        }
    }
}
.evidence-content-box {
    :global {
        padding: 20px;
        box-shadow: var(--shadow-1-inset);
        background-color: var(--bg-default);
        margin: -12px -8px;
    }
}

.event-report {
    height: calc(100vh - 100px);
    :global {
        .report-container {
            height: 100%;
            overflow-y: scroll;
            &::-webkit-scrollbar {


                display: none;
            }
        }
        h1,
        h2,
        h3,
        h4 {
            font-weight: bold;
            color: var(--text-title);
        }
        .report-content-child {
            padding: 10px 0 10px 20px;
            & > textarea {
                background: var(--bg-default);
            }
        }
        .table {
            width: 100%;
            border: solid 1px var(--border-default);
            td {
                min-width: 100px;
                text-align: center;
                padding: 5px 10px;
                .anticon {
                    font-size: 80px;
                    color: var(--text-desc);
                }
            }
        }
        .download-btn {
            position: absolute;
            right: 5%;
            top: 50%;
            text-align: center;
            cursor: pointer;
            .anticon {
                color: var(--text-blue);
                font-size: 40px;
            }
        }
        #comment-id {
            resize: none;
            width: 100%;
            padding: 5px 10px;
            border-radius: 4px;
            border-color: var(--border-default);
        }
    }
}

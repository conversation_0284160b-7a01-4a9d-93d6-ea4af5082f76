.page-om {
    :global {
        .page-top {
            margin-bottom: 20px;
        }
        .page-center {
            display: flex;
            min-height: 640px;

            max-height: 800px;
            height: calc(65vh - 50px - 45px);
            margin-bottom: 20px;
            .page-center-left {

                min-width: 400px;
                width: calc(100% - 420px);
                display: flex;
                flex-direction: column;
                .page-center-left-top {
                    min-height: 460px;
                    height: 80%;
                    margin-bottom: 20px;
                }
                .page-center-left-bottom {
                    min-height: 120px;
                    flex: 1;
                }
            }
            .page-center-right {
                min-width: 400px;
                max-width: 560px;
                width: 30%;
                margin-left: 20px;
            }
        }
        .page-bottom {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-column-gap: 20px;
        }


    }
}

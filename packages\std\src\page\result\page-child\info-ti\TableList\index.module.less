
// table
.tableList-cmp {
    :global {
        .list-item {
            margin-bottom: 16px;
            .list-item-header {
                font-weight: bolder;
                display: flex;
                align-items: center;
                border: 1px solid var(--border-default);
                border-width: 1px 0 1px 0;
                justify-content: space-between;
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                padding: 0 13px;
            }
            .list-item-table {
                padding: 10px 4px;
                .ant-table-thead > tr > th {
                    border: none;
                }
            }
        }
        .anticon-exclamation-circle {
            font-size: 14px;
        }
        .ant-table-expanded-row {
            white-space: pre;
        }
        tr {
            cursor: pointer;
        }
        .table-cmp-desc {
            line-height: 30px;
            word-break: break-all;
            .anticon {
                font-size: 14px;
                color: var(--text-red);
                margin-left: 10px;
            }
        }
        .list-item-expand {
            max-height: 500px;
            line-height: 30px;
            overflow: auto;
        }
    }
}


.asset-statistic {
    display: flex;
    height: 100%;
    flex-wrap: wrap;
    :global {
        .asset-statistic-item {
            width: 50%;
            height: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border: solid 1px var(--border-default);
            border-width: 1px 1px 0 0;
            &:nth-child(even) {
                border-right-width: 0;
            }
            &:nth-child(1) {
                border-top-width: 0;
            }
            &:nth-child(2) {
                border-top-width: 0;
            }
            .asset-statistic-name {
                padding-top: 10px;
            }
            .asset-statistic-value {
                flex: 1;
                display: flex;
                align-items: center;
            }
        }
    }
}

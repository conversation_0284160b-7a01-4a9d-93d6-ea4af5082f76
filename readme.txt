# 项目API接口文档

## 项目概述
本项目是一个高级网络行为分析系统，专注基于流量的网络行为识别与分析、威胁行为检测与追溯。
- 版本：1.0.2 开源版
- 基础路径：/d/

## API接口分类

### 1. 认证模块 (Auth)
**基础路径：** /d/

#### 1.1 用户登录
- **接口名称：** login
- **请求方法：** POST
- **接口路径：** /d/login
- **功能描述：** 用户登录认证

#### 1.2 用户登出
- **接口名称：** logout
- **请求方法：** POST
- **接口路径：** /d/logout
- **功能描述：** 用户登出

### 2. 事件模块 (Event)
**基础路径：** /d/

#### 2.1 事件查询
- **接口名称：** eventGet
- **请求方法：** POST
- **接口路径：** /d/event
- **请求参数：** req_type: 'aggre'
- **功能描述：** 获取聚合事件数据，支持IP过滤

#### 2.2 事件状态修改
- **接口名称：** eventStatusMod
- **请求方法：** POST
- **接口路径：** /d/event
- **请求参数：** req_type: 'set_proc_status'
- **功能描述：** 修改事件处理状态

#### 2.3 事件详情查询
- **接口名称：** eventInfoGet
- **请求方法：** POST
- **接口路径：** /d/event
- **请求参数：** req_type: 'scatter'
- **功能描述：** 获取事件散点图数据

#### 2.4 事件特征查询
- **接口名称：** eventFeature
- **请求方法：** POST
- **接口路径：** /d/event_feature
- **请求参数：** starttime, endtime, obj
- **功能描述：** 获取事件特征数据

#### 2.5 事件证据查询
- **接口名称：** eventEvidence
- **请求方法：** POST
- **接口路径：** /d/evidence
- **请求参数：** time(微秒级), devid
- **功能描述：** 获取数据包证据

### 3. 资产模块 (Asset)
**基础路径：** /d/

#### 3.1 资产查询
- **接口名称：** assetGet
- **请求方法：** POST
- **接口路径：** /d/asset
- **功能描述：** 通用资产查询接口

#### 3.2 IP资产查询
- **接口名称：** assetIp
- **请求方法：** POST
- **接口路径：** /d/asset
- **请求参数：** type: 'asset_ip'
- **功能描述：** 查询IP资产信息

#### 3.3 服务资产查询
- **接口名称：** assetSrv
- **请求方法：** POST
- **接口路径：** /d/asset
- **请求参数：** type: 'asset_srv'
- **功能描述：** 查询服务资产信息

#### 3.4 主机资产查询
- **接口名称：** assetHost
- **请求方法：** POST
- **接口路径：** /d/asset
- **请求参数：** type: 'asset_host'
- **功能描述：** 查询主机资产信息

#### 3.5 URL资产查询
- **接口名称：** assetUrl
- **请求方法：** POST
- **接口路径：** /d/asset
- **请求参数：** type: 'asset_url'
- **功能描述：** 查询URL资产信息

#### 3.6 资产统计信息
- **接口名称：** statinfoGet
- **请求方法：** POST
- **接口路径：** /d/statinfo
- **请求参数：** type: 'asset'
- **功能描述：** 获取资产统计信息

### 4. 特征模块 (Feature)
**基础路径：** /d/

#### 4.1 通用特征查询
- **接口名称：** featureGet
- **请求方法：** POST
- **接口路径：** /d/feature
- **功能描述：** 通用特征数据查询

#### 4.2 TCP初始化特征
- **接口名称：** featureTcpinit
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'tcpinit', limit: 0
- **功能描述：** 查询TCP连接初始化特征

#### 4.3 DNS特征
- **接口名称：** featureDns
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'dns'
- **功能描述：** 查询DNS查询特征

#### 4.4 扫描特征
- **接口名称：** featureScan
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'scan'
- **功能描述：** 查询扫描行为特征

#### 4.5 可疑特征
- **接口名称：** featureSus
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'sus'
- **功能描述：** 查询可疑行为特征

#### 4.6 黑名单特征
- **接口名称：** featureBlack
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'black'
- **功能描述：** 查询黑名单特征

#### 4.7 服务特征
- **接口名称：** featureService
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'service'
- **功能描述：** 查询服务特征

#### 4.8 监控对象特征
- **接口名称：** featureMo
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** type: 'mo'
- **功能描述：** 查询监控对象特征

### 5. TopN模块
**基础路径：** /d/

#### 5.1 TopN查询
- **接口名称：** topnGet
- **请求方法：** POST
- **接口路径：** /d/topn
- **功能描述：** 获取TopN统计数据

### 6. 配置模块 (Config)
**基础路径：** /d/

#### 6.1 设备配置
- **接口名称：** deviceApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'agent', target: 'device'
- **功能描述：** 获取设备配置信息

#### 6.2 代理配置
- **接口名称：** proxyApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'agent'
- **功能描述：** 获取代理配置信息

#### 6.3 用户配置
- **接口名称：** userApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'user'
- **功能描述：** 获取用户配置信息

#### 6.4 监控对象配置
- **接口名称：** moApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'mo'
- **功能描述：** 获取监控对象配置

#### 6.5 监控对象组配置
- **接口名称：** mogroupApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'gget', type: 'mo_group'
- **功能描述：** 获取监控对象组配置

#### 6.6 内网IP配置
- **接口名称：** internalApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'internalip'
- **功能描述：** 获取内网IP配置

#### 6.7 黑名单配置
- **接口名称：** blacklistApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'bwlist', target: 'blacklist'
- **功能描述：** 获取黑名单配置

#### 6.8 白名单配置
- **接口名称：** whitelistApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'bwlist', target: 'whitelist'
- **功能描述：** 获取白名单配置

### 7. 事件配置模块 (Event Config)
**基础路径：** /d/

#### 7.1 事件基础配置
- **接口名称：** eventConfigApiConfig
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event'
- **功能描述：** 获取事件基础配置

#### 7.2 事件类型配置
- **接口名称：** eventConfigApiType
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event_type'
- **功能描述：** 获取事件类型配置

#### 7.3 事件级别配置
- **接口名称：** eventConfigApiLevel
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event_level'
- **功能描述：** 获取事件级别配置

#### 7.4 事件详细配置
- **接口名称：** eventConfigApi
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event_config'
- **功能描述：** 获取事件详细配置

#### 7.5 监控对象事件配置
- **接口名称：** eventConfigApiMo
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'threshold', type: 'event_config'
- **功能描述：** 获取监控对象事件配置

#### 7.6 扫描事件配置
- **接口名称：** eventConfigApiScan
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'scan', type: 'event_config'
- **功能描述：** 获取扫描事件配置

#### 7.7 DoS事件配置
- **接口名称：** eventConfigApiDos
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'srv', type: 'event_config'
- **功能描述：** 获取DoS事件配置

#### 7.8 可疑事件配置
- **接口名称：** eventConfigApiSus
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'sus', type: 'event_config'
- **功能描述：** 获取可疑事件配置

#### 7.9 黑名单事件配置
- **接口名称：** eventConfigApiBlack
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'black', type: 'event_config'
- **功能描述：** 获取黑名单事件配置

#### 7.10 DNS事件配置
- **接口名称：** eventConfigApiDns
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'dns', type: 'event_config'
- **功能描述：** 获取DNS事件配置

#### 7.11 DNS隧道事件配置
- **接口名称：** eventConfigApiDnstun
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', event_type: 'dns_tun', type: 'event_config'
- **功能描述：** 获取DNS隧道事件配置

#### 7.12 事件动作配置
- **接口名称：** eventConfigApiAction
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event_action'
- **功能描述：** 获取事件动作配置

#### 7.13 事件忽略配置
- **接口名称：** eventConfigApiIgnore
- **请求方法：** POST
- **接口路径：** /d/config
- **请求参数：** op: 'get', type: 'event_ignore'
- **功能描述：** 获取事件忽略配置

### 8. 工具API模块 (Util API)
**基础路径：** /d/

#### 8.1 地理位置信息
- **接口名称：** geoinfo
- **请求方法：** POST
- **接口路径：** /d/geoinfo
- **请求参数：** ipList: [ip数组]
- **功能描述：** 获取IP地理位置信息

#### 8.2 端口信息
- **接口名称：** portinfo
- **请求方法：** POST
- **接口路径：** /d/portinfo
- **请求参数：** portlist: [端口数组]
- **功能描述：** 获取端口服务信息

#### 8.3 IP信息查询
- **接口名称：** ipInfo
- **请求方法：** POST
- **接口路径：** /d/ipinfo
- **请求参数：** iplist: ip字符串
- **功能描述：** 获取IP详细信息

#### 8.4 威胁情报查询
- **接口名称：** threatinfo
- **请求方法：** POST
- **接口路径：** /d/threatinfo
- **请求参数：** key: 查询键, op: 'get'
- **功能描述：** 获取威胁情报信息
- **超时设置：** 30秒

#### 8.5 威胁情报专业版
- **接口名称：** threatinfoPro
- **请求方法：** POST
- **接口路径：** /d/threatinfopro
- **请求参数：** list: 查询列表, type: 查询类型
- **功能描述：** 获取专业版威胁情报

### 9. 系统控制模块 (Sctl)
**基础路径：** /d/

#### 9.1 系统状态查询
- **接口名称：** sctlStat
- **请求方法：** POST
- **接口路径：** /d/sctl
- **功能描述：** 获取系统状态信息

#### 9.2 系统启动
- **接口名称：** sctlStart
- **请求方法：** POST
- **接口路径：** /d/sctl
- **请求参数：** op: 'start'
- **功能描述：** 启动系统服务

#### 9.3 系统停止
- **接口名称：** sctlStop
- **请求方法：** POST
- **接口路径：** /d/sctl
- **请求参数：** op: 'stop'
- **功能描述：** 停止系统服务

#### 9.4 系统重启
- **接口名称：** sctlRestart
- **请求方法：** POST
- **接口路径：** /d/sctl
- **请求参数：** op: 'restart'
- **功能描述：** 重启系统服务

### 10. 可疑信息模块 (SusInfo)
**基础路径：** /d/

#### 10.1 可疑信息查询
- **接口名称：** susInfoGet
- **请求方法：** POST
- **接口路径：** /d/feature
- **请求参数：** limit: 0, type: 'sus', ti_mark: 'res', endtime: 时间戳+300
- **功能描述：** 查询指定时间内的可疑连接信息

## 前端路由配置

### 主要页面路由
- `/login` - 登录页面
- `/overview` - 总览页面
  - `/overview/om` - 运维总览
  - `/overview/an` - 分析总览  
  - `/overview/ma` - 管理总览
- `/event/list` - 事件列表
- `/event/detail` - 事件详情
- `/search` - 搜索页面
- `/result` - 搜索结果页面
- `/track` - 追踪页面
- `/config` - 配置页面
  - 事件配置
  - 黑白名单配置
  - 资产配置
  - 系统配置
  - 监控对象配置
  - 目录配置

## 特殊说明

### 缓存机制
- 系统支持请求缓存，可通过 `window.appConfig.isCacheRequset` 控制
- 缓存的接口包括：asset、evidence

### 超时设置
- 威胁情报接口(threatinfo)超时时间为30秒
- 其他接口使用默认超时时间

### 请求取消
- 支持请求取消功能，特别是以下接口：
  - portinfo
  - geoinfo  
  - threatinfo
  - feature

### 错误处理
- 统一错误处理机制
- 操作失败时返回 `[{failed}]` 标识

### 事件过滤
- 支持忽略指定IP的事件，通过 `window.appConfig.ignoreEventIpArr` 配置
- 默认忽略：['0.0.0.0', '***************']

## 配置参数

### 全局配置 (window.appConfig)
- `baseUrl`: '/d/' - API基础路径
- `version`: '1.0.2' - 系统版本
- `subName`: '开源版' - 版本标识
- `eventFeatureLimit`: 100 - 事件Feature接口条数限制
- `isCacheRequset`: true - 缓存开关
- `ignoreEventSwitch`: true - 事件忽略开关
- `ignoreEventIpArr`: ['0.0.0.0', '***************'] - 忽略的IP列表

---
文档生成时间：2025-08-07
项目类型：高级网络行为分析系统
技术栈：React + Antd + MobX + Axios
